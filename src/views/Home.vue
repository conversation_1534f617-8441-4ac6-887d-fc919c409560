<script setup>
import { onMounted, ref } from 'vue'
import 'https://webapi.amap.com/loader.js'
import * as echarts from 'echarts'
import AppApi from '@/api/sys/app.js'
import StatisticApi from '@/api/sys/statistic.js'
import Util from '@/api/util.js'

const gutter = [8, 8]

const span = {
  xs: 24,
  sm: 12,
  xl: 6
}

const ready = ref(false)

const welcome = ref({
  greeting: null,
  user: {
    name: '',
    department: '',
    avatar: null
  }
})

const indicator = ref({
  ru: 0,
  cu: 0,
  dar: 0,
  urr: 0
})

// 模拟流量数据（实际项目中应该从API获取）
const trafficData = ref([
  { date: '2024-01-01', value: 120 },
  { date: '2024-01-02', value: 132 },
  { date: '2024-01-03', value: 101 },
  { date: '2024-01-04', value: 134 },
  { date: '2024-01-05', value: 90 },
  { date: '2024-01-06', value: 230 },
  { date: '2024-01-07', value: 210 },
  { date: '2024-01-08', value: 182 },
  { date: '2024-01-09', value: 191 },
  { date: '2024-01-10', value: 234 },
  { date: '2024-01-11', value: 290 },
  { date: '2024-01-12', value: 330 },
  { date: '2024-01-13', value: 310 },
  { date: '2024-01-14', value: 123 },
  { date: '2024-01-15', value: 442 },
  { date: '2024-01-16', value: 321 },
  { date: '2024-01-17', value: 90 },
  { date: '2024-01-18', value: 149 },
  { date: '2024-01-19', value: 210 },
  { date: '2024-01-20', value: 122 },
  { date: '2024-01-21', value: 133 },
  { date: '2024-01-22', value: 334 },
  { date: '2024-01-23', value: 198 },
  { date: '2024-01-24', value: 123 },
  { date: '2024-01-25', value: 125 },
  { date: '2024-01-26', value: 220 },
  { date: '2024-01-27', value: 132 },
  { date: '2024-01-28', value: 334 },
  { date: '2024-01-29', value: 189 },
  { date: '2024-01-30', value: 334 }
])

// 地图控件
let map = null

// 图表控件
let chart = null

const draw = (x, y) => {
  if (chart === null) {
    return
  }

  const _option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: x
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      type: 'line',
      data: y,
      smooth: true,
      label: {
        show: true,
        position: 'center'
      },
      areaStyle: {}
    }]
  }

  chart.setOption(_option)
}

onMounted(() => {
  ready.value = true

  const _hour = new Date().getHours()
  if (_hour < 6) {
    welcome.value.greeting = '辛苦了'
  } else if (_hour < 12) {
    welcome.value.greeting = '早安'
  } else if (_hour < 18) {
    welcome.value.greeting = '午安'
  } else {
    welcome.value.greeting = '晚安'
  }

  AppApi.getUser({
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }).then(result => {
    welcome.value.user = {
      name: result.data.name,
      department: result.data.deptFullName,
      avatar: result.data.avatarUrl || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
    }
  })

  StatisticApi.operation({
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    indicator.value.ru = result.data.ru
    indicator.value.cu = result.data.cu
    indicator.value.dar = result.data.dar * 10000 / 100.00
    indicator.value.urr = result.data.urr * 10000 / 100.00
  })

  Util.aMapJsKey({
    toast: {
      success: false
    }
  }).then(result => {
    window._AMapSecurityConfig = {
      securityJsCode: result.data.code
    }

    AMapLoader.load({
      key: result.data.key,
      version: '2.0'
    })
      .then(AMap => {
        map = new AMap.Map('map', {
          viewMode: '3D',
          center: [116.681395, 23.354091],
          zoom: 12
        })

        map.add(new AMap.Marker({
          position: [116.681395, 23.354091]
        }))
        map.add(new AMap.Marker({
          position: [116.70341, 23.365716]
        }))
      })
  })

  chart = echarts.init(document.querySelector('#chart'))
  draw(trafficData.value.map(i => i.date), trafficData.value.map(i => i.value))
})
</script>

<template>
  <template v-if="ready">
    <teleport to=".ant-tabs-tabpane-active">
      <a-page-header>
        <div class="home-page-header">
          <div>
            <div class="home-page-header-content">
              <a-avatar
                :size="70"
                :src="welcome.user.avatar"
              />
              <div class="home-page-header-content-heading">
                <div class="home-page-header-content-heading-title">
                  {{ welcome.greeting }}，
                  <template v-if="typeof welcome.user.name === 'string' && welcome.user.name">
                    <span style="color: rgba(248,113,113)">{{ welcome.user.name }}</span>，
                  </template>
                  祝您开心每一天！
                </div>
                <div class="home-page-header-content-heading-sub-title">
                  <bank-outlined /> {{ welcome.user.department }}
                </div>
              </div>
            </div>
          </div>
          <div>
            <!-- 个人统计指标 -->
            <div class="home-page-header-extra">
              <a-statistic
                :title="'项目'"
                :value="0"
              />
              <a-divider :type="'vertical'" />
              <a-statistic
                :title="'任务'"
                :value="0"
              />
              <a-divider :type="'vertical'" />
              <a-statistic
                :title="'通知'"
                :value="0"
              />
            </div>
          </div>
        </div>
      </a-page-header>
    </teleport>
  </template>

  <a-space
    :direction="'vertical'"
    style="width: 100%"
  >
    <!-- 指标 -->
    <div class="operation-container">
      <a-row
        :align="'middle'"
        :gutter="gutter"
      >
        <a-col
          :sm="span.sm"
          :xl="span.xl"
          :xs="span.xs"
        >
          <a-card :body-style="{ 'background-color': '#3598dc', 'border-radius': '4px' }">
            <a-statistic
              :title="'注册用户'"
              :value="indicator.ru + '人'"
              :value-style="{ color: '#fff' }"
              style="margin-right: 50px"
            >
              <template #suffix>
                <team-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col
          :sm="span.sm"
          :xl="span.xl"
          :xs="span.xs"
        >
          <a-card :body-style="{ 'background-color': '#e7505a', 'border-radius': '4px' }">
            <a-statistic
              :title="'在线用户'"
              :value="indicator.cu + '人'"
              :value-style="{ color: '#fff' }"
              style="margin-right: 50px"
            >
              <template #suffix>
                <desktop-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col
          :sm="span.sm"
          :xl="span.xl"
          :xs="span.xs"
        >
          <a-card :body-style="{ 'background-color': '#32c5d2', 'border-radius': '4px' }">
            <a-statistic
              :title="'日活跃率'"
              :value="indicator.dar + '%'"
              :value-style="{ color: '#fff' }"
              style="margin-right: 50px"
            >
              <template #suffix>
                <arrow-up-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col
          :sm="span.sm"
          :xl="span.xl"
          :xs="span.xs"
        >
          <a-card :body-style="{ 'background-color': '#8E44AD', 'border-radius': '4px' }">
            <a-statistic
              :title="'用户留存率'"
              :value="indicator.urr + '%'"
              :value-style="{ color: '#fff' }"
              style="margin-right: 50px"
            >
              <template #suffix>
                <history-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 地图 -->
    <a-card
      :title="'位置监控'"
      :body-style="{
        padding: '0'
      }"
    >
      <div
        id="map"
        class="container"
      />
    </a-card>

    <!-- 图表 -->
    <a-card
      :title="'流量监控'"
      :body-style="{
        padding: '0'
      }"
    >
      <div
        id="chart"
        class="container"
      />
    </a-card>
  </a-space>
</template>

<style lang="less" scoped>
@import '@/less/default';

.home-page-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;

  > div:first-child {
    margin-bottom: @margin-md;
  }

  .ant-avatar {
    flex-shrink: 0;
  }

  .home-page-header-content {
    display: flex;

    .home-page-header-content-heading {
      flex: 1 1 auto;
      margin-left: @margin-lg;
      line-height: 22px;

      .home-page-header-content-heading-title {
        margin-bottom: @margin-sm;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px
      }

      .home-page-header-content-heading-sub-title {
        color: rgba(0, 0, 0, 0.65);
        font-size: @font-size-base;
        line-height: 22px;
      }
    }
  }
}

.home-page-header-extra {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ant-statistic {
    padding: 0 32px;
  }

  .ant-statistic:last-child {
    padding-right: 0;
  }

  .ant-divider {
    height: 36px;
  }
}

.operation-container {
  .ant-statistic {
    margin-right: 0 !important;

    .ant-statistic-title {
      color: rgba(255, 255, 255, 0.7);
    }

    span.ant-statistic-content-suffix {
      float: right;
    }
  }
}

.container {
  width: 100%;
  height: 400px;
}
</style>
