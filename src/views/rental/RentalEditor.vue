<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import FileUploader from '@/components/FileUploader.vue'
import DeviceApi from '@/api/device.js'
import MediaApi from '@/api/media/record.js'
import RentalApi from '@/api/rental.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const step = ref(0)

// 表单引用
const form = ref()

const rules = {
  name: [{
    required: true,
    message: '请输入承租人姓名'
  }],
  passport: [{
    required: true,
    message: '请输入护照号码'
  }],
  country: [{
    required: true,
    message: '请输入国籍'
  }],
  contact: [{
    required: true,
    message: '请输入联系方式'
  }],
  expireTime: [{
    required: true,
    message: '请选择到期时间'
  }]
}

// 租约
const rental = ref({
  id: null,
  deviceId: null,
  lessorId: null,
  packageNo: null,
  payment: null,
  creatorName: null,
  createTime: null
})

// mifi
const devices = ref([])
const selectDevice = device => {
  devices.value.forEach(i => {
    i._selected = i.id === device.id
  })
}

// 承租人
const lessee = ref({
  id: null,
  name: '',
  passport: '',
  country: '',
  contact: '',
  expireTime: '',
  avatarId: null
})

// 流量包
const packages = ref([{
  no: 1,
  name: '10元100M套餐',
  price: 10,
  flow: '100M',
  expireTime: '30天',
  _selected: false
}, {
  no: 2,
  name: '30元500M套餐',
  price: 30,
  flow: '500M',
  expireTime: '30天',
  _selected: false
}, {
  no: 3,
  name: '8元套餐',
  price: 8,
  flow: '50M',
  expireTime: '15天',
  _selected: false
}, {
  no: 4,
  name: '20元套餐',
  price: 20,
  flow: '200M',
  expireTime: '30天',
  _selected: false
}])
const selectPackage = pkg => {
  packages.value.forEach(i => {
    i._selected = i.no === pkg.no
  })
}

// 支付方式
const payments = ref([{
  id: 'WEIXIN-NATIVE',
  name: '微信',
  icon: 'WechatOutlined',
  color: '#87d068',
  _selected: false
}, {
  id: 'ALIPAY-WAP',
  name: '支付宝',
  icon: 'AlipayCircleOutlined',
  color: '#1677ff',
  _selected: false
}])
const selectPayment = payment => {
  payments.value.forEach(i => {
    i._selected = i.id === payment.id
  })
}

const uploader = ref()

const upload = file => {
  return new Promise(resolve => {
    MediaApi.uploadForm('passport', [file], {
      toast: {
        success: false
      }
    }).then(result => {
      lessee.value.avatarId = result.data[0].id

      resolve({
        id: result.data[0].id,
        url: MediaApi.preview(result.data[0].id)
      })
    })
  })
}

const verifyPassport = () => {
  if (!lessee.value.passport) {
    FeedbackUtil.message('请先输入护照号码', 'warning')
    return
  }

  const _promise = new Promise(resolve => setTimeout(resolve, 3 * 1000))
  _promise.then(() => {
    FeedbackUtil.message('校验成功', 'success')
  })

  // 显示加载中
  FeedbackUtil.loadingBy(_promise)
}

const validated = computed(() => {
  return devices.value.some(i => i._selected) &&
      packages.value.some(i => i._selected) &&
      payments.value.some(i => i._selected) &&
      lessee.value.name &&
      lessee.value.passport &&
      lessee.value.country &&
      lessee.value.contact &&
      lessee.value.expireTime &&
      lessee.value.avatarId
})

const open = ref(false)
const url = ref('')
const submit = () => {
  if (!validated.value) {
    return
  }

  form.value.validate().then(() => {
    // 新增租约
    RentalApi.add({
      name: lessee.value.name,
      passport: lessee.value.passport,
      country: lessee.value.country,
      contact: lessee.value.contact,
      avatarId: lessee.value.avatarId
    }, {
      deviceId: devices.value
        .filter(i => i._selected)
        .map(i => i.id)[0],
      packageNo: packages.value
        .filter(i => i._selected)
        .map(i => i.no)[0],
      payment: payments.value
        .filter(i => i._selected)
        .map(i => i.id)[0],
      expireTime: lessee.value.expireTime
    }, {
      toast: {
        success: false
      }
    }).then(result => {
      // 刷新
      /* proxy.$router.push({
        query: {
          id: result.data.id
        }
      }) */

      // 显示支付窗口
      const _amount = packages.value
        .filter(i => i._selected)
        .map(i => i.price)[0]
      url.value = `http://localhost/#/rental/record/pay?amount=${_amount}`

      open.value = true
    })
  })
}

onMounted(() => {
  const _id = proxy.$route.query.id
  if (typeof _id === 'string') {
    RentalApi.get(_id, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      const _rental = result.data

      // 设置租约信息
      rental.value = {
        id: _rental.id,
        deviceId: _rental.deviceId,
        packageNo: _rental.packageNo,
        payment: _rental.payment,
        creatorName: _rental.creatorName,
        createTime: _rental.createTime
      }

      // 设置承租人信息
      lessee.value = {
        id: _rental.lessee?.id || null,
        name: _rental.lessee?.name || '-',
        passport: _rental.lessee?.passport || '-',
        country: _rental.lessee?.country || '-',
        contact: _rental.lessee?.contact || '-',
        expireTime: _rental.lessee?.expireTime || '-',
        avatarId: _rental.lessee?.avatarId || null
      }

      // 获取设备信息
      DeviceApi.get(_rental.deviceId).then(deviceResult => {
        devices.value = [{
          ...deviceResult.data,
          _selected: true
        }]
      })
    })
  } else {
    DeviceApi.me(-1, -1, null, null, null, {
      toast: { success: false }
    }).then(result => {
      devices.value = result.data.records
    })
  }
})
</script>

<template>
  <teleport to=".ant-tabs-tabpane-active">
    <a-page-header
      :sub-title="typeof proxy.$route.query.id === 'string' ? '查看租约' : '新增租约'"
      @back="() => proxy.$router.push({
        name:'admin.rental.index'
      })"
    />
  </teleport>

  <div class="layout-content-panel">
    <!-- 新增 -->
    <template v-if="proxy.$route.query.id == null">
      <a-space
        :direction="'vertical'"
        :size="'large'"
        style="width: 100%"
      >
        <a-steps
          v-model:current="step"
          :items="[{
            title: '登记用户'
          }, {
            title: '选择设备'
          }, {
            title: '选择流量包'
          }, {
            title: '选择支付方式'
          }]"
        />

        <div>
          <a-card
            v-show="step === 0"
            :bordered="false"
            :size="'small'"
          >
            <a-form
              ref="form"
              :model="lessee"
              :disabled="proxy.$route.query.id != null"
              :rules="rules"
              :label-col="{
                sm: {
                  span: 7
                },
                lg: {
                  span: 7
                }
              }"
              :wrapper-col="{
                sm: {
                  span: 17
                },
                lg: {
                  span: 10
                }
              }"
              :layout="'horizontal'"
            >
              <a-form-item
                :label="'姓名'"
                :name="'name'"
              >
                <a-input
                  v-model:value="lessee.name"
                  :allow-clear="true"
                />
              </a-form-item>

              <a-form-item
                :label="'人脸图片'"
                :name="'avatarId'"
              >
                <FileUploader
                  ref="uploader"
                  :component="'gallery'"
                  :accept="'image/*'"
                  :max="1"
                  :upload="upload"
                  :disabled="proxy.$route.query.id != null"
                />
              </a-form-item>

              <a-form-item
                :label="'护照号码'"
                :name="'passport'"
              >
                <a-input-group compact>
                  <a-input
                    v-model:value="lessee.passport"
                    :allow-clear="true"
                    style="width: calc(100% - 32px)"
                  />
                  <a-button
                    type="primary"
                    :disabled="proxy.$route.query.id != null || !lessee.passport"
                    @click="verifyPassport"
                  >
                    <template #icon>
                      <security-scan-outlined />
                    </template>
                  </a-button>
                </a-input-group>
              </a-form-item>

              <a-form-item
                :label="'国籍'"
                :name="'country'"
              >
                <a-input
                  v-model:value="lessee.country"
                  :allow-clear="true"
                />
              </a-form-item>

              <a-form-item
                :label="'联系方式'"
                :name="'contact'"
              >
                <a-input
                  v-model:value="lessee.contact"
                  :allow-clear="true"
                />
              </a-form-item>

              <a-form-item
                :label="'到期时间'"
                :name="'expireTime'"
              >
                <a-date-picker
                  v-model:value="lessee.expireTime"
                  :format="'YYYY-MM-DD'"
                />
              </a-form-item>
            </a-form>
          </a-card>

          <a-card
            v-show="step === 1"
            :bordered="false"
            :size="'small'"
          >
            <template v-if="devices.length === 0">
              <a-empty description="暂无设备" />
            </template>
            <template v-else>
              <a-row :gutter="8">
                <template
                  v-for="i in devices"
                  :key="i.id"
                >
                  <a-col
                    :xs="24"
                    :sm="12"
                    :md="8"
                    :lg="6"
                  >
                    <a-card
                      :hoverable="true"
                      :class="i._selected ? 'selected' : ''"
                      @click="selectDevice(i)"
                    >
                      <template #title>
                        {{ i.msisdn }}
                      </template>

                      <template #extra>
                        <template v-if="i._selected">
                          <check-circle-outlined
                            :style="{
                              color: '#52c41a',
                              fontSize: '18px'
                            }"
                          />
                        </template>
                      </template>

                      <a-descriptions
                        :column="1"
                        :size="'small'"
                      >
                        <a-descriptions-item :label="'iccid'">
                          {{ i.iccid }}
                        </a-descriptions-item>
                        <a-descriptions-item :label="'imsi'">
                          {{ i.imsi }}
                        </a-descriptions-item>
                      </a-descriptions>
                    </a-card>
                  </a-col>
                </template>
              </a-row>
            </template>
          </a-card>

          <a-card
            v-show="step === 2"
            :bordered="false"
            :size="'small'"
          >
            <a-row :gutter="8">
              <template
                v-for="i in packages"
                :key="i.no"
              >
                <a-col
                  :xs="24"
                  :sm="12"
                  :md="8"
                  :lg="6"
                >
                  <a-card
                    :hoverable="true"
                    :class="i._selected ? 'selected' : ''"
                    @click="selectPackage(i)"
                  >
                    <template #title>
                      {{ i.name }}
                    </template>

                    <template #extra>
                      <template v-if="i._selected">
                        <check-circle-outlined
                          :style="{
                            color: '#52c41a',
                            fontSize: '18px'
                          }"
                        />
                      </template>
                    </template>

                    <a-descriptions
                      :column="1"
                      :size="'small'"
                    >
                      <a-descriptions-item :label="'价格'">
                        {{ i.price }}
                      </a-descriptions-item>
                      <a-descriptions-item :label="'流量'">
                        {{ i.flow }}
                      </a-descriptions-item>
                      <a-descriptions-item :label="'有效期'">
                        {{ i.expireTime }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
              </template>
            </a-row>
          </a-card>

          <a-card
            v-show="step === 3"
            :bordered="false"
            :size="'small'"
          >
            <a-row :gutter="8">
              <template
                v-for="i in payments"
                :key="i.id"
              >
                <a-col
                  :xs="24"
                  :sm="12"
                  :md="8"
                  :lg="6"
                >
                  <a-card
                    :hoverable="true"
                    :class="i._selected ? 'selected' : ''"
                    @click="selectPayment(i)"
                  >
                    <div style="text-align: center;">
                      <a-avatar
                        :size="'large'"
                        :style="{
                          backgroundColor: i.color
                        }"
                      >
                        <template #icon>
                          <component :is="$icons[i.icon]" />
                        </template>
                      </a-avatar>

                      <p>{{ i.name }}</p>
                    </div>
                  </a-card>
                </a-col>
              </template>
            </a-row>
          </a-card>
        </div>

        <div style="text-align: center">
          <a-button
            :type="'primary'"
            :disabled="!validated"
            @click="submit"
          >
            <template #icon>
              <audit-outlined />
            </template>
            办理
          </a-button>
        </div>
      </a-space>
    </template>

    <!-- 查看 -->
    <template v-else>
      <a-space
        :direction="'vertical'"
        :size="'large'"
        style="width: 100%"
      >
        <a-descriptions
          :title="'租约'"
          :column="{ xs: 1, sm: 2, md: 3 }"
          :size="'small'"
          :bordered="true"
        >
          <a-descriptions-item :label="'编号'">
            {{ rental.id || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'姓名'">
            {{ lessee.name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'人脸'">
            <template v-if="lessee.avatarId">
              <a-image
                :src="MediaApi.preview(lessee.avatarId)"
                :width="100"
              />
            </template>
            <template v-else>
              -
            </template>
          </a-descriptions-item>
          <a-descriptions-item :label="'护照号码'">
            {{ lessee.passport || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'国籍'">
            {{ lessee.country || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'联系方式'">
            {{ lessee.contact || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'到期时间'">
            {{ lessee.expireTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建人员'">
            {{ rental.creatorName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建时间'">
            {{ rental.createTime || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <a-descriptions
          :title="'设备'"
          :column="{ xs: 1, sm: 2, md: 3 }"
          :size="'small'"
          :bordered="true"
        >
          <a-descriptions-item :label="'MSISDN'">
            {{ devices.find(i => i.id === rental.deviceId)?.msisdn || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'ICCID'">
            {{ devices.find(i => i.id === rental.deviceId)?.iccid || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'IMSI'">
            {{ devices.find(i => i.id === rental.deviceId)?.imsi || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <a-descriptions
          :title="'流量包'"
          :column="{ xs: 1, sm: 2, md: 3 }"
          :size="'small'"
          :bordered="true"
        >
          <a-descriptions-item :label="'名称'">
            {{ packages.find(i => i.no === rental.packageNo)?.name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'价格'">
            {{ packages.find(i => i.no === rental.packageNo)?.price || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'流量'">
            {{ packages.find(i => i.no === rental.packageNo)?.flow || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'有效期'">
            {{ packages.find(i => i.no === rental.packageNo)?.expireTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'支付方式'">
            {{ payments.find(i => i.id === rental.payment)?.name || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-space>
    </template>
  </div>

  <a-modal
    v-model:open="open"
    :body-style="{
      height: '520px'
    }"
    :centered="true"
    :closable="false"
    :footer="null"
  >
    <iframe :src="url" />
  </a-modal>
</template>

<style lang="less" scoped>
@import '@/less/default';

.ant-card.selected {
  border-color: transparent;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

.ant-modal-content {
  padding: 0 !important;

  iframe {
    border: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }
}
</style>
