<script setup>
import { ref } from 'vue'
import EdiTable from '@/components/EdiTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import RentalApi from '@/api/rental.js'
import DeviceApi from '@/api/device.js'

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: 'msisdn',
    field: 'msisdn',
    icon: 'PhoneOutlined',
    type: 'text'
  }, {
    title: '护照号',
    field: 'passport',
    icon: 'IdcardOutlined',
    type: 'text'
  }, {
    title: '姓名',
    field: 'name',
    icon: 'UserOutlined',
    type: 'text'
  }, {
    title: '办理日期',
    field: 'range',
    icon: 'CalendarOutlined',
    type: 'range'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    idField: 'id'
  },
  columns: [{
    title: 'msisdn',
    dataIndex: 'msisdn',
    sorter: true
  }, {
    title: '承租人姓名',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '护照号',
    dataIndex: 'passport',
    sorter: true
  }, {
    title: '国籍',
    dataIndex: 'country',
    sorter: true
  }, {
    title: '到期时间',
    dataIndex: 'expireTime',
    sorter: true,
    type: 'datetime'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  let _from = null
  let _to = null
  if (Array.isArray(_filters.range)) {
    _from = _filters.range[0]
    _to = _filters.range[1]
  }

  const _promise = RentalApi.search(count, index, _filters.msisdn, _filters.passport, _filters.name, _from, _to, {
    showLoading: false,
    toast: {
      success: false
    }
  })

  return new Promise((resolve, reject) => {
    _promise.then(result => {
      const _page = result.data

      _page.records.forEach(i => {
        i.name = i.lessee.name
        i.passport = i.lessee.passport
        i.country = i.lessee.country
        i.expireTime = i.lessee.expireTime
      })

      const _ids = _page.records.map(i => i.id)

      const _promise = _ids.length === 0
        ? Promise.resolve({
          data: []
        })
        : DeviceApi.find(_ids, {
          showLoading: false,
          toast: {
            success: false
          }
        })

      _promise.then(result => {
        _page.records.forEach(i => {
          const _device = result.data.find(j => j.id === i.deviceId)
          i.msisdn = _device == null ? null : _device.msisdn
        })

        resolve(_page)
      })
        .catch(() => {
          resolve({
            total: 0,
            records: []
          })
        })
    })
  })
}

const remove = record => {
  // 注意：后端没有提供删除接口，这里暂时返回 Promise.resolve()
  // 如果需要删除功能，需要在后端添加对应的接口
  return Promise.resolve()
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <EdiTable
      ref="table"
      :mapper="tableOptions.mapper"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :addable="true"
      :load="load"
      :remove="remove"
    />
  </div>
</template>
