<template>
  <div class="pay-container">
    <!-- 支付信息展示 -->
    <div
      v-if="paymentStep === 'info'"
      class="pay-info-section"
    >
      <div class="pay-header">
        <div class="pay-icon">
          <PayCircleOutlined />
        </div>
        <h2 class="pay-title">
          确认支付
        </h2>
      </div>

      <div class="amount-display">
        <div class="amount-label">
          支付金额
        </div>
        <div class="amount-value">
          ¥{{ formatAmount(amount) }}
        </div>
      </div>

      <div class="pay-method">
        <div class="method-item selected">
          <WalletOutlined class="method-icon" />
          <span class="method-text">余额支付</span>
          <CheckCircleFilled class="method-check" />
        </div>
      </div>

      <div class="pay-actions">
        <a-button
          type="primary"
          size="large"
          block
          class="pay-button"
          @click="showPasswordInput"
        >
          立即支付
        </a-button>
      </div>
    </div>

    <!-- 密码输入界面 -->
    <div
      v-if="paymentStep === 'password'"
      class="password-section"
    >
      <div class="password-header">
        <div
          class="back-button"
          @click="backToInfo"
        >
          <LeftOutlined />
        </div>
        <h3 class="password-title">
          输入支付密码
        </h3>
      </div>

      <div class="amount-summary">
        <span>支付金额：¥{{ formatAmount(amount) }}</span>
      </div>

      <div class="password-input-container">
        <div class="password-dots">
          <div
            v-for="i in 6"
            :key="i"
            class="password-dot"
            :class="{ filled: password.length >= i }"
          >
            <div
              v-if="password.length >= i"
              class="dot-fill"
            />
          </div>
        </div>

        <input
          ref="passwordInput"
          v-model="password"
          type="password"
          maxlength="6"
          class="hidden-input"
          @input="onPasswordInput"
          @focus="inputFocused = true"
          @blur="inputFocused = false"
        >
      </div>

      <div class="keyboard-tip">
        <span>请输入6位支付密码</span>
      </div>
    </div>

    <!-- 支付处理中 -->
    <div
      v-if="paymentStep === 'processing'"
      class="processing-section"
    >
      <div class="processing-content">
        <div class="countdown-main">
          <div class="countdown-container">
            <div class="countdown-ring" />
            <div class="countdown-ring-pulse" />
            <div class="countdown-number-large">
              {{ countdown }}
            </div>
          </div>
        </div>
        <h3 class="processing-title">
          支付处理中...
        </h3>
        <p class="processing-desc">
          请稍候，正在为您处理支付
        </p>
        <div class="processing-amount">
          ¥{{ formatAmount(amount) }}
        </div>
      </div>
    </div>

    <!-- 支付成功 -->
    <div
      v-if="paymentStep === 'success'"
      class="success-section"
    >
      <div class="success-content">
        <div class="success-icon">
          <CheckCircleFilled />
        </div>
        <h2 class="success-title">
          支付成功
        </h2>
        <div class="success-amount">
          ¥{{ formatAmount(amount) }}
        </div>
        <p class="success-desc">
          您的支付已完成
        </p>

        <div class="success-actions">
          <a-button
            type="primary"
            size="large"
            block
            @click="goBack"
          >
            完成
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  PayCircleOutlined,
  WalletOutlined,
  CheckCircleFilled,
  LeftOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const amount = ref(0)
const paymentStep = ref('info') // info, password, processing, success
const password = ref('')
const inputFocused = ref(false)
const passwordInput = ref(null)
const countdown = ref(0)
const countdownTimer = ref(null)

// 获取路由参数中的金额
onMounted(() => {
  amount.value = parseFloat(route.query.amount) || 0
})

// 格式化金额显示
const formatAmount = (value) => {
  return value.toFixed(2)
}

// 显示密码输入界面
const showPasswordInput = () => {
  paymentStep.value = 'password'
  nextTick(() => {
    if (passwordInput.value) {
      passwordInput.value.focus()
    }
  })
}

// 返回支付信息界面
const backToInfo = () => {
  paymentStep.value = 'info'
  password.value = ''
}

// 密码输入处理
const onPasswordInput = () => {
  if (password.value.length === 6) {
    // 密码输入完成，开始支付处理
    processPayment()
  }
}

// 处理支付
const processPayment = () => {
  paymentStep.value = 'processing'
  countdown.value = 1

  // 启动倒计时
  countdownTimer.value = setInterval(() => {
    countdown.value++
    if (countdown.value > 5) {
      clearInterval(countdownTimer.value)
      paymentStep.value = 'success'
    }
  }, 1000)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 清理定时器
const cleanup = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  cleanup()
})
</script>

<style scoped lang="less">
// 通用变量
@primary-color: #1890ff;
@success-color: #52c41a;
@text-color: #262626;
@text-color-secondary: #8c8c8c;
@border-radius: 16px;
@box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

// 容器基础样式
.pay-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

// 通用卡片样式
.pay-info-section,
.password-section,
.processing-section,
.success-section {
  background: white;
  border-radius: @border-radius;
  padding: 32px 24px;
  width: 100%;
  max-width: 400px;
  box-shadow: @box-shadow;
  text-align: center;
}

.password-section {
  padding: 24px;
  text-align: left;
}

// 支付信息页面
.pay-header {
  text-align: center;
  margin-bottom: 32px;

  .pay-icon {
    font-size: 48px;
    color: @primary-color;
    margin-bottom: 16px;
  }

  .pay-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: @text-color;
  }
}

.amount-display {
  margin-bottom: 32px;

  .amount-label {
    font-size: 14px;
    color: @text-color-secondary;
    margin-bottom: 8px;
  }

  .amount-value {
    font-size: 36px;
    font-weight: 700;
    color: @text-color;
  }
}

.pay-method {
  margin-bottom: 32px;

  .method-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &.selected {
      border-color: @primary-color;
      background: #f6ffed;
    }

    .method-icon {
      font-size: 20px;
      color: @primary-color;
      margin-right: 12px;
    }

    .method-text {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      color: @text-color;
    }

    .method-check {
      font-size: 20px;
      color: @success-color;
    }
  }
}

.pay-button {
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, @primary-color, #096dd9);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #40a9ff, @primary-color);
  }
}

// 密码输入页面
.password-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .back-button {
    font-size: 18px;
    color: @text-color-secondary;
    cursor: pointer;
    padding: 8px;
    margin-right: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      color: @text-color;
    }
  }

  .password-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: @text-color;
  }
}

.amount-summary {
  text-align: center;
  margin-bottom: 32px;
  font-size: 16px;
  color: #595959;
}

.password-input-container {
  position: relative;
  margin-bottom: 24px;
}

.password-dots {
  display: flex;
  justify-content: center;
  gap: 12px;

  .password-dot {
    width: 40px;
    height: 40px;
    border: 2px solid #d9d9d9;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.filled {
      border-color: @primary-color;
      background: #f6ffed;
    }

    .dot-fill {
      width: 12px;
      height: 12px;
      background: @primary-color;
      border-radius: 50%;
    }
  }
}

.hidden-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 10;
  font-size: 16px;
}

.keyboard-tip {
  text-align: center;
  color: @text-color-secondary;
  font-size: 14px;
}

// 支付处理页面
.processing-section {
  padding: 48px 24px;
}

.processing-content {
  .countdown-main {
    margin-bottom: 32px;
    display: flex;
    justify-content: center;
    align-items: center;

    .countdown-container {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;

      .countdown-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 3px solid #e6f7ff;
        border-radius: 50%;
        animation: ringRotate 2s linear infinite;
      }

      .countdown-ring-pulse {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid @primary-color;
        border-radius: 50%;
        animation: ringPulse 1s ease-in-out infinite;
        opacity: 0.6;
      }

      .countdown-number-large {
        font-size: 48px;
        font-weight: 700;
        color: @primary-color;
        line-height: 1;
        z-index: 2;
        animation: numberPulse 1s ease-in-out infinite;
      }
    }
  }

  .processing-title {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 600;
    color: @text-color;
  }

  .processing-desc {
    margin: 0 0 24px 0;
    font-size: 14px;
    color: @text-color-secondary;
  }

  .processing-amount {
    font-size: 24px;
    font-weight: 700;
    color: @primary-color;
  }
}

// 支付成功页面
.success-section {
  padding: 48px 24px;
}

.success-content {
  .success-icon {
    font-size: 64px;
    color: @success-color;
    margin-bottom: 24px;
    animation: successPulse 0.6s ease-out;
  }

  .success-title {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: @text-color;
  }

  .success-amount {
    font-size: 32px;
    font-weight: 700;
    color: @success-color;
    margin-bottom: 12px;
  }

  .success-desc {
    margin: 0 0 32px 0;
    font-size: 14px;
    color: @text-color-secondary;
  }
}

.success-actions {
  .ant-btn {
    height: 48px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, @success-color, #389e0d);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #73d13d, @success-color);
    }
  }
}

// 动画效果
@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes ringRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ringPulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

@keyframes numberPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 480px) {
  .pay-container {
    padding: 16px;
  }

  .pay-info-section,
  .password-section,
  .processing-section,
  .success-section {
    padding: 24px 20px;
  }

  .amount-display .amount-value {
    font-size: 28px;
  }

  .success-content .success-amount {
    font-size: 24px;
  }
}
</style>
