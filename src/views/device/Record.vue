<script setup>
import { onMounted, ref } from 'vue'
import EdiTable from '@/components/EdiTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import DeviceApi from '@/api/device.js'
import PermissionApi from '@/api/sec/permission.js'

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: 'MSISDN',
    field: 'msisdn',
    icon: 'PhoneOutlined',
    type: 'text'
  }, {
    title: 'ICCID',
    field: 'iccid',
    icon: 'CreditCardOutlined',
    type: 'text'
  }, {
    title: 'IMSI',
    field: 'imsi',
    icon: 'IdcardOutlined',
    type: 'text'
  }, {
    title: '租户ID',
    field: 'tenantId',
    icon: 'TeamOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/device',
    idField: 'id'
  },
  columns: [{
    title: 'MSISDN',
    dataIndex: 'msisdn'
  }, {
    title: 'ICCID',
    dataIndex: 'iccid'
  }, {
    title: 'IMSI',
    dataIndex: 'imsi'
  }, {
    title: '租户ID',
    dataIndex: 'tenantId'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime'
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    DeviceApi.search(count, index, _filters.msisdn, _filters.iccid, _filters.imsi, _filters.tenantId, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const remove = record => {
  return DeviceApi.remove(record.id)
}

const addable = ref(false)
const editable = ref(false)
const removeable = ref(false)

const ready = ref(false)
onMounted(() => {
  PermissionApi.find({
    showLoading: false,
    toast: {
      success: false
    }
  })
    .then(result => {
      result.data.forEach(i => {
        if (new RegExp(i.name).test('device.save')) {
          addable.value = true
          editable.value = true
        }

        if (new RegExp(i.name).test('device.remove')) {
          removeable.value = true
        }
      })
    })
    .finally(() => {
      ready.value = true
    })
})
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <template v-if="ready">
      <EdiTable
        ref="table"
        :mapper="tableOptions.mapper"
        :actions="tableOptions.actions"
        :columns="tableOptions.columns"
        :addable="addable"
        :load="load"
        :edit="editable"
        :remove="removeable ? remove : false"
      />
    </template>
    <template v-else>
      <a-skeleton :active="true" />
    </template>
  </div>
</template>
