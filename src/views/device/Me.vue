<script setup>
import { onMounted, ref, computed } from 'vue'
import * as echarts from 'echarts'
import MeApi from '@/api/client/me.js'
import AppApi from '@/api/sys/app.js'
import '@/utils/date.js'

// 用户信息
const userInfo = ref({
  name: '',
  avatar: '',
  phone: ''
})

// 套餐信息
const packageInfo = ref({
  name: '',
  totalFlow: 0,
  usedFlow: 0,
  remainingFlow: 0,
  expireDate: '',
  status: 'active'
})

// 流量使用趋势数据
const usageTrend = ref([])

// 图表实例
let chart = null

// 加载状态
const loading = ref(true)

// 计算使用百分比
const usagePercentage = computed(() => {
  if (packageInfo.value.totalFlow === 0) return 0
  return Math.round((packageInfo.value.usedFlow / packageInfo.value.totalFlow) * 100)
})

// 格式化流量显示
const formatFlow = (bytes) => {
  if (bytes === 0) return '0 MB'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 绘制流量趋势图
const drawChart = () => {
  if (!chart) return
  
  const dates = usageTrend.value.map(item => item.date)
  const values = usageTrend.value.map(item => item.usage)
  
  const option = {
    title: {
      text: '最近7天流量使用趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const data = params[0]
        return `${data.axisValue}<br/>流量使用: ${formatFlow(data.value)}`
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        formatter: function(value) {
          return new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return formatFlow(value)
        }
      }
    },
    series: [{
      name: '流量使用',
      type: 'line',
      data: values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: '#1890ff'
      },
      itemStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(24, 144, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(24, 144, 255, 0.05)'
          }]
        }
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chart.setOption(option)
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const result = await AppApi.getUser({
      showLoading: false,
      toast: { success: false, error: false }
    })
    userInfo.value = {
      name: result.data.name || '用户',
      avatar: result.data.avatarUrl || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
      phone: result.data.mp || ''
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 加载套餐信息
const loadPackageInfo = async () => {
  try {
    // 模拟数据，实际应该调用API
    // const result = await MeApi.getPackageInfo()
    
    // 模拟套餐数据
    packageInfo.value = {
      name: '30元500M套餐',
      totalFlow: 500 * 1024 * 1024, // 500MB转换为字节
      usedFlow: 320 * 1024 * 1024,  // 320MB转换为字节
      remainingFlow: 180 * 1024 * 1024, // 180MB转换为字节
      expireDate: '2024-12-31',
      status: 'active'
    }
  } catch (error) {
    console.error('加载套餐信息失败:', error)
  }
}

// 加载流量使用趋势
const loadUsageTrend = async () => {
  try {
    // 模拟数据，实际应该调用API
    // const result = await MeApi.getUsageTrend()
    
    // 模拟最近7天的流量使用数据
    const today = new Date()
    const trendData = []
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      trendData.push({
        date: date.toISOString().split('T')[0],
        usage: Math.floor(Math.random() * 50 + 20) * 1024 * 1024 // 20-70MB随机数据
      })
    }
    
    usageTrend.value = trendData
  } catch (error) {
    console.error('加载流量趋势失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadUserInfo(),
      loadPackageInfo(),
      loadUsageTrend()
    ])
    
    // 重新绘制图表
    setTimeout(() => {
      drawChart()
    }, 100)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 初始化图表
  chart = echarts.init(document.getElementById('usage-chart'))
  
  // 加载数据
  await refreshData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
})
</script>

<template>
  <div class="me-container">
    <a-spin :spinning="loading">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 用户信息卡片 -->
        <a-card>
          <div class="user-info">
            <a-avatar :size="64" :src="userInfo.avatar" />
            <div class="user-details">
              <h2>{{ userInfo.name }}</h2>
              <p v-if="userInfo.phone">手机号: {{ userInfo.phone }}</p>
            </div>
            <a-button type="primary" @click="refreshData" :loading="loading">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新数据
            </a-button>
          </div>
        </a-card>

        <!-- 套餐信息卡片 -->
        <a-card title="我的套餐">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="套餐名称"
                :value="packageInfo.name"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="到期时间"
                :value="packageInfo.expireDate"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-statistic
                title="套餐状态"
                value="正常使用"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>

        <!-- 流量使用情况卡片 -->
        <a-card title="流量使用情况">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="总流量"
                :value="formatFlow(packageInfo.totalFlow)"
                :value-style="{ color: '#1890ff' }"
              >
                <template #suffix>
                  <CloudOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="已使用"
                :value="formatFlow(packageInfo.usedFlow)"
                :value-style="{ color: '#ff4d4f' }"
              >
                <template #suffix>
                  <DownloadOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic
                title="剩余流量"
                :value="formatFlow(packageInfo.remainingFlow)"
                :value-style="{ color: '#52c41a' }"
              >
                <template #suffix>
                  <UploadOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
          
          <!-- 使用进度条 -->
          <div style="margin-top: 24px">
            <div style="margin-bottom: 8px">
              <span>使用进度: {{ usagePercentage }}%</span>
            </div>
            <a-progress 
              :percent="usagePercentage" 
              :stroke-color="usagePercentage > 80 ? '#ff4d4f' : usagePercentage > 60 ? '#faad14' : '#52c41a'"
            />
          </div>
        </a-card>

        <!-- 流量使用趋势图 -->
        <a-card>
          <div id="usage-chart" style="width: 100%; height: 400px;"></div>
        </a-card>
      </a-space>
    </a-spin>
  </div>
</template>

<style scoped>
.me-container {
  padding: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-details {
  flex: 1;
}

.user-details h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 500;
}

.user-details p {
  margin: 0;
  color: #666;
}

@media (max-width: 768px) {
  .me-container {
    padding: 16px;
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
