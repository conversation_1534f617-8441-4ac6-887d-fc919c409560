<script setup>
import { getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import DeviceApi from '@/api/device.js'

const { proxy } = getCurrentInstance()

const fields = [{
  title: 'MSISDN',
  field: 'msisdn',
  type: 'text',
  config: {
    required: true,
    placeholder: '请输入MSISDN号码'
  }
}, {
  title: 'ICCID',
  field: 'iccid',
  type: 'text',
  config: {
    required: true,
    placeholder: '请输入ICCID'
  }
}, {
  title: 'IMSI',
  field: 'imsi',
  type: 'text',
  config: {
    required: true,
    placeholder: '请输入IMSI'
  }
}, {
  title: '租户ID',
  field: 'tenantId',
  type: 'text',
  config: {
    required: true,
    placeholder: '请输入租户ID'
  }
}, {
  title: '设备状态',
  field: 'status',
  type: 'select',
  config: {
    required: true,
    options: [{
      label: '正常',
      value: 'NORMAL'
    }, {
      label: '停用',
      value: 'DISABLED'
    }, {
      label: '故障',
      value: 'FAULT'
    }]
  }
}, {
  title: '备注',
  field: 'description',
  type: 'textarea',
  config: {
    placeholder: '请输入设备描述信息'
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = DeviceApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DeviceApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const form = ref()

onMounted(() => {
  if (proxy.$route.query.id) {
    DeviceApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单
        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      status: 'NORMAL'
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>
</template>
