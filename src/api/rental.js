import HttpRequest from '@/utils/http.js'

export default {
  // 获取单个租赁记录
  get (id, options) {
    return new HttpRequest().ajax('/rental/get', {
      id
    }, options)
  },
  // 搜索租赁记录
  search (count, index, msisdn, passport, name, from, to, options) {
    return new HttpRequest().ajax('/rental/search', {
      count,
      index,
      msisdn,
      passport,
      name,
      from,
      to
    }, options)
  },
  // 添加租赁记录
  add (lessee, rental, payment, options) {
    return new HttpRequest().ajax('/rental/add', {
      lessee,
      rental,
      payment
    }, options)
  }
}
