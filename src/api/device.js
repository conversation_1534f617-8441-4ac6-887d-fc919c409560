import HttpRequest from '@/utils/http.js'

export default {
  // 读取详情
  get (id, options) {
    return new HttpRequest().ajax('/device/get', {
      id
    }, options)
  },
  // 搜索设备
  search (count, index, msisdn, iccid, imsi, tenantId, options) {
    return new HttpRequest().ajax('/device/search', {
      count,
      index,
      msisdn,
      iccid,
      imsi,
      tenantId
    }, options)
  },
  // 我的设备
  me (count, index, msisdn, iccid, imsi, options) {
    return new HttpRequest().ajax('/device/me', {
      count,
      index,
      msisdn,
      iccid,
      imsi
    }, options)
  },
  // 根据ID列表查找设备
  find (ids, options) {
    return new HttpRequest().ajax('/device/find', {
      ids
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/device/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/device/remove', {
      id
    }, options)
  }
}
