import HttpRequest from '@/utils/http.js'

export default {
  // 搜索订单
  search (count, index, tenantId, type, msisdn, from, to, status, options) {
    return new HttpRequest().ajax('/order/search', {
      count,
      index,
      tenantId,
      type,
      msisdn,
      from,
      to,
      status
    }, options)
  },
  // 我的订单
  me (count, index, type, msisdn, from, to, status, options) {
    return new HttpRequest().ajax('/order/me', {
      count,
      index,
      type,
      msisdn,
      from,
      to,
      status
    }, options)
  },
  // 订购套餐
  order (msisdn, packageNo, options) {
    return new HttpRequest().ajax('/order/order', {
      msisdn,
      packageNo
    }, options)
  }
}
